import React from 'react';
import { useTranslation } from 'react-i18next';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Card,
  Form,
  FormItem,
  Input,
  Select,
  IconCard,
  Textarea,
} from '@/shared/components/common';
import { useCreateWarehouse } from '../../hooks/useWarehouseQuery';
import { CreateWarehouseDto, WarehouseTypeEnum } from '../../types/warehouse.types';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';

// Schema cho form
const getWarehouseSchema = (t: (key: string) => string) =>
  z.object({
    name: z.string().min(1, t('business:warehouse.form.namePlaceholder')),
    description: z.string().optional(),
    type: z.nativeEnum(WarehouseTypeEnum, {
      errorMap: () => ({
        message: t('business:warehouse.form.typePlaceholder'),
      }),
    }),
  });

export type CreateWarehouseFormValues = z.infer<ReturnType<typeof getWarehouseSchema>>;

interface CreateWarehouseFormProps {
  initialData?: Partial<CreateWarehouseFormValues>;
  onSubmit?: (data: CreateWarehouseFormValues) => void;
  onCancel?: () => void;
}

/**
 * Component form tạo kho mới
 */
const CreateWarehouseForm: React.FC<CreateWarehouseFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
}) => {
  const { t } = useTranslation(['business', 'common']);
  const notification = useSmartNotification();

  // Mutation để tạo kho mới
  const { mutateAsync: createWarehouse, isPending: isCreating } = useCreateWarehouse();

  // Schema cho form
  const warehouseSchema = getWarehouseSchema(t);

  // Khởi tạo form
  const form = useForm<CreateWarehouseFormValues>({
    resolver: zodResolver(warehouseSchema),
    defaultValues: {
      name: initialData?.name || '',
      description: initialData?.description || '',
      type: initialData?.type || WarehouseTypeEnum.PHYSICAL,
    },
  });

  // Xử lý submit form
  const handleSubmit = async (values: CreateWarehouseFormValues) => {
    try {
      // Tạo kho mới
      await createWarehouse(values as CreateWarehouseDto);
      notification.success({ message: t('business:warehouse.createSuccess') });

      // Gọi callback onSubmit nếu có
      if (onSubmit) {
        onSubmit(values);
      }
    } catch (error) {
      console.error('Error creating warehouse:', error);
      notification.error({
        message: t('business:warehouse.createError')
      });
    }
  };

  return (
    <Card title={t('business:warehouse.add')}>
      <Form
        schema={warehouseSchema}
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        onSubmit={handleSubmit as any}
        className="p-4 space-y-4"
      >
        <div className="grid grid-cols-1 gap-4">
          <FormItem
            name="name"
            label={t('business:warehouse.name')}
            required
          >
            <Input
              fullWidth
              placeholder={t('business:warehouse.form.namePlaceholder')}
              {...form.register('name')}
            />
          </FormItem>

          <FormItem
            name="description"
            label={t('business:warehouse.desc')}
          >
            <Textarea
              rows={4}
              placeholder={t('business:warehouse.form.descriptionPlaceholder')}
              {...form.register('description')}
            />
          </FormItem>

          <FormItem
            name="type"
            label={t('business:warehouse.type')}
            required
          >
            <Controller
              control={form.control}
              name="type"
              render={({ field }) => (
                <Select
                  fullWidth
                  placeholder={t('business:warehouse.form.selectType')}
                  options={[
                    {
                      value: WarehouseTypeEnum.PHYSICAL,
                      label: t('business:warehouse.types.PHYSICAL'),
                    },
                    {
                      value: WarehouseTypeEnum.VIRTUAL,
                      label: t('business:warehouse.types.VIRTUAL'),
                    },
                  ]}
                  value={field.value}
                  onChange={field.onChange}
                />
              )}
            />
          </FormItem>
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <IconCard
            icon="x"
            variant="secondary"
            size="md"
            title={t('common:cancel')}
            onClick={onCancel}
          />
          <IconCard
            icon="plus"
            variant="primary"
            size="md"
            title={t('common:create')}
            onClick={() => {
              // Trigger form submit programmatically
              const form = document.querySelector('form');
              if (form) {
                form.requestSubmit();
              }
            }}
            isLoading={isCreating}
          />
        </div>
      </Form>
    </Card>
  );
};

export default CreateWarehouseForm;
